import { roleValidator } from "../../../enumValidators";
import { doc } from "convex-helpers/validators";
import { Infer, v } from "convex/values";
import { Nullable } from "../../../../helpers/nullableObjectReturnValidator";
import { Doc } from "../../../../convex/_generated/dataModel";
import schema from "../../../../convex/schema";

/*──────────────────────── GENERAL ────────────────────────*/

export const invitationResponseDTOValidator = {
    ...doc(schema, "invitations").fields,
    invitedByUser: Nullable(v.object({
        _id: v.id("users"),
        email: v.string(),
        firstName: v.optional(v.string()),
        lastName: v.optional(v.string()),
    })),
    acceptedUser: Nullable(v.object({
        _id: v.id("users"),
        email: v.string(),
        firstName: v.optional(v.string()),
        lastName: v.optional(v.string()),
    })),
};

export const invitationResponseDTOObjectValidator = v.object(invitationResponseDTOValidator);

export type InvitationResponseDTO = Doc<"invitations"> & {
    invitedByUser: {
        _id: Doc<"users">["_id"];
        email: string;
        firstName?: string;
        lastName?: string;
    } | null;
    acceptedUser: {
        _id: Doc<"users">["_id"];
        email: string;
        firstName?: string;
        lastName?: string;
    } | null;
};

/*──────────────────────── CREATE ────────────────────────*/

export const InvitationsCreateRequestDTOValidator = {
    email: v.string(),
    roleSuggested: v.optional(v.array(roleValidator)),
    message: v.optional(v.string()),
    expiresAt: v.optional(v.string()),
};

export const InvitationsCreateResponseDTOValidator = {
    invitation: v.object(invitationResponseDTOValidator),
    invitationUrl: v.string(),
};

export const InvitationsCreateRequestDTOObjectValidator = v.object(InvitationsCreateRequestDTOValidator);
export const InvitationsCreateResponseDTOObjectValidator = v.object(InvitationsCreateResponseDTOValidator);

export type InvitationsCreateRequestDTO = Infer<typeof InvitationsCreateRequestDTOObjectValidator>;
export type InvitationsCreateResponseDTO = Infer<typeof InvitationsCreateResponseDTOObjectValidator>;

/*──────────────────────── LIST ────────────────────────*/

export const InvitationsListRequestDTOValidator = {
    search: v.optional(v.string()),
    status: v.optional(v.union(
        v.literal("pending"),
        v.literal("accepted"),
        v.literal("expired"),
        v.literal("cancelled")
    )),
    role: v.optional(v.array(roleValidator)),
    limit: v.number(),
    offset: v.optional(v.number()),
};

export const InvitationsListResponseDTOValidator = {
    data: v.array(v.object(invitationResponseDTOValidator)),
    total: v.number(),
};

export const InvitationsListRequestDTOObjectValidator = v.object(InvitationsListRequestDTOValidator);
export const InvitationsListResponseDTOObjectValidator = v.object(InvitationsListResponseDTOValidator);

export type InvitationsListRequestDTO = Infer<typeof InvitationsListRequestDTOObjectValidator>;
export type InvitationsListResponseDTO = Infer<typeof InvitationsListResponseDTOObjectValidator>;

/*──────────────────────── GET ────────────────────────*/

export const InvitationsGetRequestDTOValidator = {
    id: v.id("invitations"),
};

export const InvitationsGetResponseDTOValidator = invitationResponseDTOValidator;

export const InvitationsGetRequestDTOObjectValidator = v.object(InvitationsGetRequestDTOValidator);
export const InvitationsGetResponseDTOObjectValidator = v.object(InvitationsGetResponseDTOValidator);

export type InvitationsGetRequestDTO = Infer<typeof InvitationsGetRequestDTOObjectValidator>;
export type InvitationsGetResponseDTO = Infer<typeof InvitationsGetResponseDTOObjectValidator>;

/*──────────────────────── UPDATE ────────────────────────*/

export const InvitationsUpdateRequestDTOValidator = {
    id: v.id("invitations"),
    roleSuggested: v.optional(v.array(roleValidator)),
    message: v.optional(v.string()),
    expiresAt: v.optional(v.string()),
};

export const InvitationsUpdateResponseDTOValidator = invitationResponseDTOValidator;

export const InvitationsUpdateRequestDTOObjectValidator = v.object(InvitationsUpdateRequestDTOValidator);
export const InvitationsUpdateResponseDTOObjectValidator = v.object(InvitationsUpdateResponseDTOValidator);

export type InvitationsUpdateRequestDTO = Infer<typeof InvitationsUpdateRequestDTOObjectValidator>;
export type InvitationsUpdateResponseDTO = Infer<typeof InvitationsUpdateResponseDTOObjectValidator>;

/*──────────────────────── DELETE ────────────────────────*/

export const InvitationsDeleteRequestDTOValidator = {
    id: v.id("invitations"),
};

export const InvitationsDeleteResponseDTOValidator = {
    success: v.boolean(),
};

export const InvitationsDeleteRequestDTOObjectValidator = v.object(InvitationsDeleteRequestDTOValidator);
export const InvitationsDeleteResponseDTOObjectValidator = v.object(InvitationsDeleteResponseDTOValidator);

export type InvitationsDeleteRequestDTO = Infer<typeof InvitationsDeleteRequestDTOObjectValidator>;
export type InvitationsDeleteResponseDTO = Infer<typeof InvitationsDeleteResponseDTOObjectValidator>;

/*──────────────────────── RESEND ────────────────────────*/

export const InvitationsResendRequestDTOValidator = {
    id: v.id("invitations"),
    message: v.optional(v.string()),
};

export const InvitationsResendResponseDTOValidator = {
    success: v.boolean(),
    newToken: v.string(),
    invitationUrl: v.string(),
};

export const InvitationsResendRequestDTOObjectValidator = v.object(InvitationsResendRequestDTOValidator);
export const InvitationsResendResponseDTOObjectValidator = v.object(InvitationsResendResponseDTOValidator);

export type InvitationsResendRequestDTO = Infer<typeof InvitationsResendRequestDTOObjectValidator>;
export type InvitationsResendResponseDTO = Infer<typeof InvitationsResendResponseDTOObjectValidator>;

/*──────────────────────── CANCEL ────────────────────────*/

export const InvitationsCancelRequestDTOValidator = {
    id: v.id("invitations"),
};

export const InvitationsCancelResponseDTOValidator = {
    success: v.boolean(),
};

export const InvitationsCancelRequestDTOObjectValidator = v.object(InvitationsCancelRequestDTOValidator);
export const InvitationsCancelResponseDTOObjectValidator = v.object(InvitationsCancelResponseDTOValidator);

export type InvitationsCancelRequestDTO = Infer<typeof InvitationsCancelRequestDTOObjectValidator>;
export type InvitationsCancelResponseDTO = Infer<typeof InvitationsCancelResponseDTOObjectValidator>;

/*──────────────────────── ACCEPT ────────────────────────*/

export const InvitationsAcceptRequestDTOValidator = {
    token: v.string(),
    email: v.string(),
};

export const InvitationsAcceptResponseDTOValidator = {
    success: v.boolean(),
    invitation: v.object(invitationResponseDTOValidator),
    redirectUrl: v.optional(v.string()),
};

export const InvitationsAcceptRequestDTOObjectValidator = v.object(InvitationsAcceptRequestDTOValidator);
export const InvitationsAcceptResponseDTOObjectValidator = v.object(InvitationsAcceptResponseDTOValidator);

export type InvitationsAcceptRequestDTO = Infer<typeof InvitationsAcceptRequestDTOObjectValidator>;
export type InvitationsAcceptResponseDTO = Infer<typeof InvitationsAcceptResponseDTOObjectValidator>;

/*──────────────────────── BULK OPERATIONS ────────────────────────*/

export const InvitationsBulkDeleteRequestDTOValidator = {
    ids: v.array(v.id("invitations")),
};

export const InvitationsBulkDeleteResponseDTOValidator = {
    success: v.boolean(),
    deletedCount: v.number(),
    errors: v.array(v.string()),
};

export const InvitationsBulkDeleteRequestDTOObjectValidator = v.object(InvitationsBulkDeleteRequestDTOValidator);
export const InvitationsBulkDeleteResponseDTOObjectValidator = v.object(InvitationsBulkDeleteResponseDTOValidator);

export type InvitationsBulkDeleteRequestDTO = Infer<typeof InvitationsBulkDeleteRequestDTOObjectValidator>;
export type InvitationsBulkDeleteResponseDTO = Infer<typeof InvitationsBulkDeleteResponseDTOObjectValidator>;

export const InvitationsBulkResendRequestDTOValidator = {
    ids: v.array(v.id("invitations")),
    message: v.optional(v.string()),
};

export const InvitationsBulkResendResponseDTOValidator = {
    success: v.boolean(),
    resentCount: v.number(),
    errors: v.array(v.string()),
};

export const InvitationsBulkResendRequestDTOObjectValidator = v.object(InvitationsBulkResendRequestDTOValidator);
export const InvitationsBulkResendResponseDTOObjectValidator = v.object(InvitationsBulkResendResponseDTOValidator);

export type InvitationsBulkResendRequestDTO = Infer<typeof InvitationsBulkResendRequestDTOObjectValidator>;
export type InvitationsBulkResendResponseDTO = Infer<typeof InvitationsBulkResendResponseDTOObjectValidator>;
